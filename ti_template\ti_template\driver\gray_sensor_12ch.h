#ifndef __GRAY_SENSOR_12CH_H
#define __GRAY_SENSOR_12CH_H

#include "bsp_system.h"

#ifdef __cplusplus
extern "C" {
#endif

/* PCA9555寄存器定义 */
#define INPUT_PORT_REGISTER0                    0x00   // 输入端口寄存器0，控制IO00-IO07
#define INPUT_PORT_REGISTER1                    0x01   // 输入端口寄存器1，控制IO10-IO17
#define OUTPUT_PORT_REGISTER0                   0x02   // 输出端口寄存器0，控制IO00-IO07
#define OUTPUT_PORT_REGISTER1                   0x03   // 输出端口寄存器1，控制IO10-IO17
#define POLARITY_INVERSION_PORT_REGISTER0       0x04   // 极性反转端口寄存器0，控制IO00-IO07
#define POLARITY_INVERSION_PORT_REGISTER1       0x05   // 极性反转端口寄存器1，控制IO10-IO17
#define CONFIG_PORT_REGISTER0                   0x06   // 配置端口寄存器0，控制IO00-IO07
#define CONFIG_PORT_REGISTER1                   0x07   // 配置端口寄存器1，控制IO10-IO17

/* 设备地址定义 */
#define GRAY_SENSOR_ADDR                        0x40   // 12路灰度传感器I2C地址
#define HOST_WRITE_COMMAND                      0x00   // 写命令
#define HOST_READ_COMMAND                       0x01   // 读命令

/* 灰度传感器状态结构体 */
typedef struct
{
    uint8_t bit1    :1;
    uint8_t bit2    :1;
    uint8_t bit3    :1;
    uint8_t bit4    :1;
    uint8_t bit5    :1;
    uint8_t bit6    :1;
    uint8_t bit7    :1;
    uint8_t bit8    :1;
    uint8_t bit9    :1;
    uint8_t bit10   :1;
    uint8_t bit11   :1;
    uint8_t bit12   :1;
    uint8_t bit13   :1;
    uint8_t bit14   :1;
    uint8_t bit15   :1;
    uint8_t bit16   :1;
} GraySensorBits_t;

/* 灰度传感器状态联合体 */
typedef union
{
    uint16_t state;
    GraySensorBits_t bits;
} GraySensorState_t;

/* 全局变量声明 */
extern GraySensorState_t g_gray_sensor_state;
extern float g_gray_position[2];              // [0]:10mm线宽位置, [1]:20mm线宽位置
extern float g_gray_position_history[2][20];  // 位置历史记录
extern uint32_t g_gray_error_count;           // 错误计数

/* 函数声明 */
uint8_t gray_sensor_init(void);                                    // 初始化12路灰度传感器
uint16_t gray_sensor_read_12bit(uint8_t slave_addr);              // 读取12位灰度数据
void gray_sensor_process_10mm_line(void);                         // 处理10mm线宽数据
void gray_sensor_process_20mm_line(void);                         // 处理20mm线宽数据
float gray_sensor_get_position_10mm(void);                        // 获取10mm线宽位置
float gray_sensor_get_position_20mm(void);                        // 获取20mm线宽位置
uint16_t gray_sensor_get_raw_data(void);                          // 获取原始数据

#ifdef __cplusplus
}
#endif

#endif /* __GRAY_SENSOR_12CH_H */

/**
 * @file integrated_line_following.c
 * @brief 完整的循线控制集成示例
 * @note 展示如何将12路灰度传感器与电机控制、PID算法、编码器等完整集成
 */

#include "bsp_system.h"

/* 全局控制参数 */
typedef struct {
    float base_speed;           // 基础速度
    float max_speed;            // 最大速度
    float min_speed;            // 最小速度
    LineWidthType_t line_mode;  // 线宽模式
    uint8_t pid_enabled;        // PID使能标志
    uint8_t debug_enabled;      // 调试输出使能
} LineFollowingConfig_t;

static LineFollowingConfig_t g_config = {
    .base_speed = 50.0f,
    .max_speed = 80.0f,
    .min_speed = 20.0f,
    .line_mode = LINE_WIDTH_10MM,
    .pid_enabled = 1,
    .debug_enabled = 1
};

/* 系统状态 */
typedef struct {
    GrayStatus_t gray_status;   // 灰度传感器状态
    float current_position;     // 当前线位置
    float last_position;        // 上次线位置
    uint32_t lost_line_time;    // 丢线时间
    uint32_t normal_time;       // 正常运行时间
    uint8_t system_ready;       // 系统就绪标志
} SystemStatus_t;

static SystemStatus_t g_system_status = {0};

/**
 * @brief 系统初始化
 */
static void system_init(void)
{
    // 硬件初始化
    SYSCFG_DL_init();
    user_config();
    scheduler_init();
    
    // 等待系统稳定
    delay_ms(100);
    
    // 检查传感器状态
    if (get_gray_sensor_status() == GRAY_STATUS_ERROR) {
        my_printf(UART_0_INST, "ERROR: Gray sensor initialization failed!\r\n");
        g_system_status.system_ready = 0;
        return;
    }
    
    // 初始化系统状态
    g_system_status.gray_status = GRAY_STATUS_NORMAL;
    g_system_status.current_position = 0.0f;
    g_system_status.last_position = 0.0f;
    g_system_status.lost_line_time = 0;
    g_system_status.normal_time = 0;
    g_system_status.system_ready = 1;
    
    my_printf(UART_0_INST, "Integrated Line Following System Ready!\r\n");
    my_printf(UART_0_INST, "Base Speed: %.1f, Line Mode: %s, PID: %s\r\n",
              g_config.base_speed,
              (g_config.line_mode == LINE_WIDTH_10MM) ? "10mm" : "20mm",
              g_config.pid_enabled ? "ON" : "OFF");
}

/**
 * @brief 更新系统状态
 */
static void update_system_status(void)
{
    // 更新传感器状态
    g_system_status.gray_status = get_gray_sensor_status();
    
    // 更新位置信息
    g_system_status.last_position = g_system_status.current_position;
    g_system_status.current_position = calculate_line_position_weighted(g_config.line_mode);
    
    // 更新时间统计
    if (g_system_status.gray_status == GRAY_STATUS_NORMAL) {
        g_system_status.normal_time++;
        g_system_status.lost_line_time = 0;
    } else if (g_system_status.gray_status == GRAY_STATUS_LOST) {
        g_system_status.lost_line_time++;
    }
}

/**
 * @brief PID循线控制
 */
static void pid_line_control(void)
{
    static float integral = 0;
    static float last_error = 0;
    
    // PID参数
    float kp = 3.0f;
    float ki = 0.05f;
    float kd = 1.0f;
    
    // 计算误差
    float error = g_system_status.current_position;
    
    // PID计算
    integral += error;
    float derivative = error - last_error;
    
    // 积分限幅
    if (integral > 50) integral = 50;
    if (integral < -50) integral = -50;
    
    // PID输出
    float pid_output = kp * error + ki * integral + kd * derivative;
    
    // 计算电机速度
    float left_speed = g_config.base_speed - pid_output;
    float right_speed = g_config.base_speed + pid_output;
    
    // 速度限制
    left_speed = GRAY_CLAMP(left_speed, -g_config.max_speed, g_config.max_speed);
    right_speed = GRAY_CLAMP(right_speed, -g_config.max_speed, g_config.max_speed);
    
    // 设置电机速度
    motor_set_l(left_speed);
    motor_set_r(right_speed);
    
    // 更新历史数据
    last_error = error;
    
    // 调试输出
    if (g_config.debug_enabled) {
        static uint32_t debug_count = 0;
        if (++debug_count >= 200) {
            debug_count = 0;
            my_printf(UART_0_INST, "PID: E=%.1f, P=%.1f, I=%.1f, D=%.1f, L=%.1f, R=%.1f\r\n",
                      error, kp * error, ki * integral, kd * derivative, left_speed, right_speed);
        }
    }
}

/**
 * @brief 简单比例控制
 */
static void simple_proportional_control(void)
{
    float kp = 2.5f;
    float turn_factor = g_system_status.current_position * kp;
    
    float left_speed = g_config.base_speed - turn_factor;
    float right_speed = g_config.base_speed + turn_factor;
    
    // 速度限制
    left_speed = GRAY_CLAMP(left_speed, -g_config.max_speed, g_config.max_speed);
    right_speed = GRAY_CLAMP(right_speed, -g_config.max_speed, g_config.max_speed);
    
    motor_set_l(left_speed);
    motor_set_r(right_speed);
}

/**
 * @brief 丢线处理
 */
static void handle_line_lost(void)
{
    if (g_system_status.lost_line_time < 50) {
        // 短时间丢线，保持最后的控制输出
        return;
    } else if (g_system_status.lost_line_time < 200) {
        // 中等时间丢线，减速搜索
        float search_speed = g_config.base_speed * 0.3f;
        
        if (g_system_status.last_position > 0) {
            // 线在右边，向右搜索
            motor_set_l(search_speed);
            motor_set_r(-search_speed * 0.5f);
        } else {
            // 线在左边，向左搜索
            motor_set_l(-search_speed * 0.5f);
            motor_set_r(search_speed);
        }
    } else {
        // 长时间丢线，停止
        motor_set_l(0);
        motor_set_r(0);
        
        if (g_config.debug_enabled && (g_system_status.lost_line_time % 500 == 0)) {
            my_printf(UART_0_INST, "Line lost for %lu ms, stopped.\r\n", 
                      g_system_status.lost_line_time);
        }
    }
}

/**
 * @brief 错误处理
 */
static void handle_error(void)
{
    // 停止电机
    motor_set_l(0);
    motor_set_r(0);
    
    // 输出错误信息
    static uint32_t error_count = 0;
    if (++error_count >= 1000) {
        error_count = 0;
        my_printf(UART_0_INST, "Sensor error! Please check I2C connection.\r\n");
    }
}

/**
 * @brief 自适应线宽检测
 */
static void adaptive_line_width_detection(void)
{
    uint16_t raw_data = get_gray_raw_data();
    
    // 计算激活的传感器数量
    uint8_t active_sensors = 0;
    for (int i = 0; i < 12; i++) {
        if (GRAY_GET_BIT(raw_data, i)) {
            active_sensors++;
        }
    }
    
    // 根据激活传感器数量判断线宽
    static uint8_t mode_change_count = 0;
    LineWidthType_t new_mode = g_config.line_mode;
    
    if (active_sensors >= 4 && g_config.line_mode == LINE_WIDTH_10MM) {
        mode_change_count++;
        if (mode_change_count >= 10) {
            new_mode = LINE_WIDTH_20MM;
        }
    } else if (active_sensors <= 2 && g_config.line_mode == LINE_WIDTH_20MM) {
        mode_change_count++;
        if (mode_change_count >= 10) {
            new_mode = LINE_WIDTH_10MM;
        }
    } else {
        mode_change_count = 0;
    }
    
    // 切换模式
    if (new_mode != g_config.line_mode) {
        g_config.line_mode = new_mode;
        mode_change_count = 0;
        
        if (g_config.debug_enabled) {
            my_printf(UART_0_INST, "Line width mode changed to: %s\r\n",
                      (new_mode == LINE_WIDTH_10MM) ? "10mm" : "20mm");
        }
    }
}

/**
 * @brief 主控制循环
 */
void integrated_line_following_main(void)
{
    // 系统初始化
    system_init();
    
    if (!g_system_status.system_ready) {
        my_printf(UART_0_INST, "System initialization failed!\r\n");
        return;
    }
    
    my_printf(UART_0_INST, "Starting integrated line following...\r\n");
    
    while (1) {
        // 运行调度器
        scheduler_run();
        
        // 更新系统状态
        update_system_status();
        
        // 自适应线宽检测
        adaptive_line_width_detection();
        
        // 根据传感器状态执行相应控制
        switch (g_system_status.gray_status) {
            case GRAY_STATUS_NORMAL:
                // 正常循线控制
                if (g_config.pid_enabled) {
                    pid_line_control();
                } else {
                    simple_proportional_control();
                }
                break;
                
            case GRAY_STATUS_LOST:
                // 丢线处理
                handle_line_lost();
                break;
                
            case GRAY_STATUS_ERROR:
                // 错误处理
                handle_error();
                break;
        }
        
        // 状态监控和调试输出
        static uint32_t status_count = 0;
        if (g_config.debug_enabled && ++status_count >= 1000) {
            status_count = 0;
            my_printf(UART_0_INST, "Status: Gray=%d, Pos=%.1f, Mode=%s, Time=%lu\r\n",
                      g_system_status.gray_status,
                      g_system_status.current_position,
                      (g_config.line_mode == LINE_WIDTH_10MM) ? "10mm" : "20mm",
                      g_system_status.normal_time);
        }
    }
}

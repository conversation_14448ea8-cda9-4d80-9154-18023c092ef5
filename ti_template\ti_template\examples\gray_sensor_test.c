/**
 * @file gray_sensor_test.c
 * @brief 12路灰度传感器测试示例
 * <AUTHOR> Template Team
 * @date 2024
 */

#include "bsp_system.h"

/**
 * @brief 基础循线控制示例
 * @note 展示如何使用12路灰度传感器进行循线控制
 */
void basic_line_following_demo(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    user_config();
    scheduler_init();
    
    // 设置基础参数
    float base_speed = 40.0f;  // 基础速度
    float kp = 2.0f;           // 比例系数
    
    my_printf(UART_0_INST, "12-Channel Gray Sensor Line Following Demo Started\r\n");
    
    while (1) {
        // 任务调度
        scheduler_run();
        
        // 检查传感器状态
        GrayStatus_t status = get_gray_sensor_status();
        
        if (status == GRAY_STATUS_NORMAL) {
            // 正常循线模式
            
            // 获取线位置（使用10mm线宽算法）
            float line_pos = calculate_line_position_weighted(LINE_WIDTH_10MM);
            
            // 简单的比例控制
            float turn_factor = line_pos * kp;
            
            // 差速控制
            motor_set_l(base_speed - turn_factor);
            motor_set_r(base_speed + turn_factor);
            
            // 调试输出
            static uint32_t debug_count = 0;
            if (++debug_count >= 500) {
                debug_count = 0;
                my_printf(UART_0_INST, "Normal: Pos=%.1f, L=%.1f, R=%.1f\r\n",
                          line_pos, base_speed - turn_factor, base_speed + turn_factor);
            }
        }
        else if (status == GRAY_STATUS_LOST) {
            // 丢线处理
            motor_set_l(0);
            motor_set_r(0);
            
            static uint32_t lost_count = 0;
            if (++lost_count >= 1000) {
                lost_count = 0;
                my_printf(UART_0_INST, "Line Lost! Stopping motors.\r\n");
            }
        }
        else {
            // 错误状态处理
            motor_set_l(0);
            motor_set_r(0);
            my_printf(UART_0_INST, "Sensor Error! Please check connection.\r\n");
            delay_ms(1000);
        }
        
        // 状态指示
        static uint32_t led_timer = 0;
        if (uwTick - led_timer > 500) {
            // LED闪烁表示系统正常
            // DL_GPIO_togglePins(GPIO_LEDS_PORT, GPIO_LEDS_USER_LED_1_PIN);
            led_timer = uwTick;
        }
    }
}

/**
 * @brief 高级循线控制示例
 * @note 展示如何结合PID控制器使用12路灰度传感器
 */
void advanced_line_following_demo(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    user_config();
    scheduler_init();
    
    // PID参数
    float base_speed = 50.0f;
    
    my_printf(UART_0_INST, "Advanced Line Following with PID Started\r\n");
    
    while (1) {
        // 任务调度
        scheduler_run();
        
        // 检查传感器状态
        GrayStatus_t status = get_gray_sensor_status();
        
        if (status == GRAY_STATUS_NORMAL) {
            // 获取线位置误差
            float line_error = calculate_line_position_weighted(LINE_WIDTH_10MM);
            
            // 使用现有的PID控制器
            // 注意：需要确保PID控制器已经初始化
            // float pid_output = pid_calculate_positional(&pid_line, line_error);
            
            // 简化的PID控制（示例）
            static float last_error = 0;
            static float integral = 0;
            
            float kp = 3.0f, ki = 0.1f, kd = 0.5f;
            
            integral += line_error;
            float derivative = line_error - last_error;
            float pid_output = kp * line_error + ki * integral + kd * derivative;
            last_error = line_error;
            
            // 限制积分项
            if (integral > 50) integral = 50;
            if (integral < -50) integral = -50;
            
            // 差速控制
            float left_speed = base_speed - pid_output;
            float right_speed = base_speed + pid_output;
            
            // 速度限制
            if (left_speed > 100) left_speed = 100;
            if (left_speed < -100) left_speed = -100;
            if (right_speed > 100) right_speed = 100;
            if (right_speed < -100) right_speed = -100;
            
            motor_set_l(left_speed);
            motor_set_r(right_speed);
            
            // 调试输出
            static uint32_t debug_count = 0;
            if (++debug_count >= 200) {
                debug_count = 0;
                my_printf(UART_0_INST, "PID: Err=%.1f, Out=%.1f, L=%.1f, R=%.1f\r\n",
                          line_error, pid_output, left_speed, right_speed);
            }
        }
        else {
            // 异常状态处理
            motor_set_l(0);
            motor_set_r(0);
            
            if (status == GRAY_STATUS_LOST) {
                my_printf(UART_0_INST, "Line lost, searching...\r\n");
                // 可以添加搜索算法
            } else {
                my_printf(UART_0_INST, "Sensor error!\r\n");
            }
            delay_ms(100);
        }
    }
}

/**
 * @brief 传感器数据监控示例
 * @note 用于调试和校准传感器
 */
void sensor_monitor_demo(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    user_config();
    scheduler_init();
    
    my_printf(UART_0_INST, "12-Channel Gray Sensor Monitor Started\r\n");
    my_printf(UART_0_INST, "Format: Raw_Data, Pos10mm, Pos20mm, Status\r\n");
    
    while (1) {
        // 任务调度
        scheduler_run();
        
        // 获取传感器数据
        uint16_t raw_data = get_gray_raw_data();
        float pos_10mm = calculate_line_position_weighted(LINE_WIDTH_10MM);
        float pos_20mm = calculate_line_position_weighted(LINE_WIDTH_20MM);
        GrayStatus_t status = get_gray_sensor_status();
        
        // 输出详细信息
        my_printf(UART_0_INST, "0x%04X, %.1f, %.1f, %d | ", 
                  raw_data, pos_10mm, pos_20mm, status);
        
        // 输出二进制位状态
        for (int i = 11; i >= 0; i--) {
            my_printf(UART_0_INST, "%d", (raw_data >> i) & 1);
        }
        my_printf(UART_0_INST, "\r\n");
        
        delay_ms(200); // 5Hz输出频率
    }
}

/**
 * @brief 双线宽自适应示例
 * @note 展示如何根据线宽自动切换算法
 */
void adaptive_line_width_demo(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    user_config();
    scheduler_init();
    
    float base_speed = 45.0f;
    LineWidthType_t current_mode = LINE_WIDTH_10MM;
    
    my_printf(UART_0_INST, "Adaptive Line Width Demo Started\r\n");
    
    while (1) {
        // 任务调度
        scheduler_run();
        
        // 获取原始数据用于线宽判断
        uint16_t raw_data = get_gray_raw_data();
        
        // 简单的线宽检测算法（计算连续1的个数）
        uint8_t line_width = 0;
        for (int i = 0; i < 12; i++) {
            if ((raw_data >> i) & 1) line_width++;
        }
        
        // 根据线宽切换模式
        if (line_width >= 4) {
            current_mode = LINE_WIDTH_20MM;
        } else {
            current_mode = LINE_WIDTH_10MM;
        }
        
        // 获取对应模式的位置
        float line_pos = calculate_line_position_weighted(current_mode);
        
        // 控制逻辑
        if (get_gray_sensor_status() == GRAY_STATUS_NORMAL) {
            float turn_factor = line_pos * 2.5f;
            motor_set_l(base_speed - turn_factor);
            motor_set_r(base_speed + turn_factor);
            
            // 调试输出
            static uint32_t debug_count = 0;
            if (++debug_count >= 300) {
                debug_count = 0;
                my_printf(UART_0_INST, "Mode: %s, Width: %d, Pos: %.1f\r\n",
                          (current_mode == LINE_WIDTH_10MM) ? "10mm" : "20mm",
                          line_width, line_pos);
            }
        } else {
            motor_set_l(0);
            motor_set_r(0);
        }
    }
}

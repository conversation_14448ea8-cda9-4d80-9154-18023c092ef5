# 12路灰度传感器集成说明

## 概述
本文档说明了如何将12路灰度传感器项目的功能集成到ti_template智能小车项目中，实现完整的循线功能。

## 集成内容

### 1. 新增驱动文件

#### 软件I2C驱动
- **头文件**: `driver/gray_soft_i2c.h`
- **实现文件**: `user/gray_soft_i2c.c`
- **功能**: 实现软件I2C通信协议，用于与PCA9555芯片通信

#### 12路灰度传感器驱动
- **头文件**: `driver/gray_sensor_12ch.h`
- **实现文件**: `user/gray_sensor_12ch.c`
- **功能**: 
  - 读取12路灰度传感器数据
  - 处理10mm和20mm线宽的位置计算
  - 提供线位置检测算法

### 2. 引脚配置

#### I2C通信引脚
| 功能 | 引脚 | GPIO | 包号引脚 | 说明 |
|------|------|------|----------|------|
| SCL | PA0 | GPIOA.0 | 33 | I2C时钟线 |
| SDA | PA1 | GPIOA.1 | 34 | I2C数据线 |

**注意**: 这些引脚替代了原本未配置的I2C功能，与现有系统完全兼容。

### 3. 系统配置更新

#### ti_msp_dl_config.h
- 添加了`GRAY_I2C_PORT`、`GRAY_I2C_SCL_PIN`、`GRAY_I2C_SDA_PIN`等宏定义
- 配置了I2C引脚的IOMUX设置

#### ti_msp_dl_config.c
- 在`SYSCFG_DL_GPIO_init()`中添加了I2C引脚初始化代码
- 配置SCL和SDA为数字输出模式

#### bsp_system.h
- 移除了原有的灰度传感器相关头文件
- 添加了新的12路灰度传感器头文件

### 4. 应用层集成

#### gray_app.h/c 更新
- 重写了灰度传感器应用层代码
- 提供了统一的API接口：
  - `user_gray_init()`: 初始化12路灰度传感器
  - `user_gray_task()`: 灰度传感器任务处理
  - `calculate_line_position_weighted()`: 计算加权线位置
  - `get_gray_sensor_status()`: 获取传感器状态
  - `get_gray_raw_data()`: 获取原始数据

## 功能特性

### 1. 双线宽支持
- **10mm线宽**: 适用于标准循线场景
- **20mm线宽**: 适用于宽线循线场景
- 自动处理不同线宽的位置计算算法

### 2. 智能状态检测
- **正常状态**: 传感器正常检测到线
- **丢线状态**: 检测到丢线情况
- **错误状态**: 传感器通信错误

### 3. 位置计算算法
- 基于12位二进制状态的精确位置计算
- 支持-11到+11的位置范围
- 包含历史数据滤波和错误处理

### 4. 调试功能
- 串口输出原始数据和位置信息
- 可配置的调试输出频率
- 状态监控和错误计数

## 使用方法

### 1. 硬件连接
```
MSPM0G3507          12路灰度传感器模块
┌─────────────┐     ┌─────────────────┐
│ PA0 (SCL)   ├─────┤ SCL             │
│ PA1 (SDA)   ├─────┤ SDA             │
│ 3.3V        ├─────┤ VCC             │
│ GND         ├─────┤ GND             │
└─────────────┘     └─────────────────┘
```

### 2. 软件调用
```c
// 在main函数中，调度器会自动初始化
scheduler_init(); // 包含user_gray_init()

// 在主循环中，调度器会自动运行灰度任务
while(1) {
    scheduler_run(); // 包含user_gray_task()
}

// 获取线位置（在PID控制中使用）
float line_pos = calculate_line_position_weighted(LINE_WIDTH_10MM);
```

### 3. PID控制集成
```c
// 在PID控制函数中使用
void pid_line_following(void) {
    float line_error = calculate_line_position_weighted(LINE_WIDTH_10MM);
    float pid_output = pid_calculate(&pid_line, line_error);
    
    // 差速控制
    motor_set_l(base_speed - pid_output);
    motor_set_r(base_speed + pid_output);
}
```

## 兼容性说明

### 1. 引脚兼容性
- 使用的PA0和PA1引脚原本未被使用
- 不与现有的PWM、编码器、串口等功能冲突
- 完全兼容现有的电机控制系统

### 2. 系统兼容性
- 保持原有的调度器架构
- 兼容现有的PID控制系统
- 不影响电机驱动和编码器功能

### 3. 代码兼容性
- 保持原有的API接口风格
- 向后兼容现有的循线控制逻辑
- 可以无缝替换原有的灰度传感器

## 调试和测试

### 1. 串口输出
连接串口调试工具，可以看到以下信息：
```
12-channel gray sensor initialized successfully!
Gray: 0x0060, Pos10: 0.0, Pos20: 0.0, Status: 0
Gray: 0x0030, Pos10: -2.0, Pos20: -2.0, Status: 0
```

### 2. 状态监控
- `Status: 0` - 正常状态
- `Status: 1` - 丢线状态  
- `Status: 2` - 错误状态

### 3. 位置范围
- 位置值范围: -11 到 +11
- 0表示线在中央
- 负值表示线偏左，正值表示线偏右

## 注意事项

1. **I2C上拉电阻**: 确保SCL和SDA线上有4.7kΩ上拉电阻
2. **电源稳定性**: 确保3.3V电源稳定，避免传感器误读
3. **线缆屏蔽**: 注意I2C信号线的屏蔽，避免电机干扰
4. **调试输出**: 生产环境中可以关闭调试输出以提高性能

## 扩展功能

1. **自适应阈值**: 可以添加自动校准功能
2. **多传感器融合**: 可以结合其他传感器提高精度
3. **高级滤波**: 可以添加卡尔曼滤波等高级算法
4. **路径记录**: 可以记录循线路径用于分析

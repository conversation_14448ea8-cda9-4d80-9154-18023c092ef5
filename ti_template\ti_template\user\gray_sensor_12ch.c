#include "gray_sensor_12ch.h"
#include "gray_soft_i2c.h"

/* 全局变量定义 */
GraySensorState_t g_gray_sensor_state = {0};
float g_gray_position[2] = {0};                    // [0]:10mm线宽位置, [1]:20mm线宽位置
float g_gray_position_history[2][20] = {0};        // 位置历史记录
uint32_t g_gray_error_count = 0;                   // 错误计数

/**
 * @brief 初始化12路灰度传感器
 * @retval 0: 成功, 1: 失败
 */
uint8_t gray_sensor_init(void)
{
    // 检测I2C设备是否存在
    if (gray_i2c_check_device(GRAY_SENSOR_ADDR) != 0)
    {
        return 1; // 设备不存在
    }
    
    // 清零状态变量
    g_gray_sensor_state.state = 0;
    g_gray_position[0] = 0;
    g_gray_position[1] = 0;
    g_gray_error_count = 0;
    
    return 0; // 初始化成功
}

/**
 * @brief 读取12位灰度传感器数据
 * @param slave_addr: 从机地址
 * @retval 12位灰度数据
 */
uint16_t gray_sensor_read_12bit(uint8_t slave_addr)
{
    uint8_t high_data, low_data;
    uint16_t bit12_data;
    
    gray_i2c_start();
    gray_i2c_send_byte_no_ack(slave_addr);                    // 写入从机地址
    gray_i2c_wait_ack();
    gray_i2c_send_byte_no_ack(INPUT_PORT_REGISTER0);          // 写入要读取的寄存器地址
    gray_i2c_wait_ack();
    gray_i2c_start();                                         // 重新开始信号
    gray_i2c_send_byte_no_ack(slave_addr | HOST_READ_COMMAND); // 发送从机地址，设置为读取
    gray_i2c_wait_ack();
    low_data = gray_i2c_read_byte();
    gray_i2c_ack();
    high_data = gray_i2c_read_byte();
    gray_i2c_nack();
    gray_i2c_stop();
    
    bit12_data = (uint16_t)(high_data << 8 | low_data) & 0x0FFF;
    return bit12_data;
}

/**
 * @brief 获取原始灰度数据
 * @retval 12位原始数据
 */
uint16_t gray_sensor_get_raw_data(void)
{
    g_gray_sensor_state.state = gray_sensor_read_12bit(GRAY_SENSOR_ADDR << 1);
    return g_gray_sensor_state.state;
}

/**
 * @brief 处理10mm线宽的灰度数据
 */
void gray_sensor_process_10mm_line(void)
{
    // 保存历史数据
    for(uint16_t i = 19; i > 0; i--)
    {
        g_gray_position_history[0][i] = g_gray_position_history[0][i-1];
    }
    g_gray_position_history[0][0] = g_gray_position[0];
    
    // 根据灰度状态计算位置
    switch(g_gray_sensor_state.state)
    {
        case 0x0001: g_gray_position[0] = -11; g_gray_error_count /= 2; break; // 000000000001b
        case 0x0003: g_gray_position[0] = -10; g_gray_error_count /= 2; break; // 000000000011b
        case 0x0002: g_gray_position[0] = -9;  g_gray_error_count /= 2; break; // 000000000010b
        case 0x0006: g_gray_position[0] = -8;  g_gray_error_count /= 2; break; // 000000000110b
        case 0x0004: g_gray_position[0] = -7;  g_gray_error_count /= 2; break; // 000000000100b
        case 0x000C: g_gray_position[0] = -6;  g_gray_error_count /= 2; break; // 000000001100b
        case 0x0008: g_gray_position[0] = -5;  g_gray_error_count /= 2; break; // 000000001000b
        case 0x0018: g_gray_position[0] = -4;  g_gray_error_count /= 2; break; // 000000011000b
        case 0x0010: g_gray_position[0] = -3;  g_gray_error_count /= 2; break; // 000000010000b
        case 0x0030: g_gray_position[0] = -2;  g_gray_error_count /= 2; break; // 000000110000b
        case 0x0020: g_gray_position[0] = -1;  g_gray_error_count /= 2; break; // 000000100000b
        case 0x0060: g_gray_position[0] = 0;   g_gray_error_count /= 2; break; // 000001100000b
        case 0x0040: g_gray_position[0] = 1;   g_gray_error_count /= 2; break; // 000001000000b
        case 0x00C0: g_gray_position[0] = 2;   g_gray_error_count /= 2; break; // 000011000000b
        case 0x0080: g_gray_position[0] = 3;   g_gray_error_count /= 2; break; // 000010000000b
        case 0x0180: g_gray_position[0] = 4;   g_gray_error_count /= 2; break; // 000110000000b
        case 0x0100: g_gray_position[0] = 5;   g_gray_error_count /= 2; break; // 000100000000b
        case 0x0300: g_gray_position[0] = 6;   g_gray_error_count /= 2; break; // 001100000000b
        case 0x0200: g_gray_position[0] = 7;   g_gray_error_count /= 2; break; // 001000000000b
        case 0x0600: g_gray_position[0] = 8;   g_gray_error_count /= 2; break; // 011000000000b
        case 0x0400: g_gray_position[0] = 9;   g_gray_error_count /= 2; break; // 010000000000b
        case 0x0C00: g_gray_position[0] = 10;  g_gray_error_count /= 2; break; // 110000000000b
        case 0x0800: g_gray_position[0] = 11;  g_gray_error_count /= 2; break; // 100000000000b
        case 0x0000: g_gray_position[0] = g_gray_position_history[0][0]; g_gray_error_count++; break; // 全黑
        default:     // 其他情况，使用历史数据
        {
            g_gray_position[0] = g_gray_position_history[0][0];
            g_gray_error_count++;
        }
    }
}

/**
 * @brief 处理20mm线宽的灰度数据
 */
void gray_sensor_process_20mm_line(void)
{
    // 保存历史数据
    for(uint16_t i = 19; i > 0; i--)
    {
        g_gray_position_history[1][i] = g_gray_position_history[1][i-1];
    }
    g_gray_position_history[1][0] = g_gray_position[1];
    
    // 根据灰度状态计算位置
    switch(g_gray_sensor_state.state)
    {
        case 0x0001: g_gray_position[1] = -11; break; // 000000000001b
        case 0x0003: g_gray_position[1] = -10; break; // 000000000011b
        case 0x0002: g_gray_position[1] = -9;  break; // 000000000010b
        case 0x0007: g_gray_position[1] = -9;  break; // 000000000111b
        case 0x0006: g_gray_position[1] = -8;  break; // 000000000110b
        case 0x0004: g_gray_position[1] = -7;  break; // 000000000100b
        case 0x000E: g_gray_position[1] = -7;  break; // 000000001110b
        case 0x000C: g_gray_position[1] = -6;  break; // 000000001100b
        case 0x0008: g_gray_position[1] = -5;  break; // 000000001000b
        case 0x001C: g_gray_position[1] = -5;  break; // 000000011100b
        case 0x0018: g_gray_position[1] = -4;  break; // 000000011000b
        case 0x0010: g_gray_position[1] = -3;  break; // 000000010000b
        case 0x0038: g_gray_position[1] = -3;  break; // 000000111000b
        case 0x0030: g_gray_position[1] = -2;  break; // 000000110000b
        case 0x0020: g_gray_position[1] = -1;  break; // 000000100000b
        case 0x0070: g_gray_position[1] = -1;  break; // 000001110000b
        case 0x0060: g_gray_position[1] = 0;   break; // 000001100000b
        case 0x0040: g_gray_position[1] = 1;   break; // 000001000000b
        case 0x00E0: g_gray_position[1] = 1;   break; // 000011100000b
        case 0x00C0: g_gray_position[1] = 2;   break; // 000011000000b
        case 0x0080: g_gray_position[1] = 3;   break; // 000010000000b
        case 0x01C0: g_gray_position[1] = 3;   break; // 000111000000b
        case 0x0180: g_gray_position[1] = 4;   break; // 000110000000b
        case 0x0100: g_gray_position[1] = 5;   break; // 000100000000b
        case 0x0380: g_gray_position[1] = 5;   break; // 001110000000b
        case 0x0300: g_gray_position[1] = 6;   break; // 001100000000b
        case 0x0200: g_gray_position[1] = 7;   break; // 001000000000b
        case 0x0700: g_gray_position[1] = 7;   break; // 011100000000b
        case 0x0600: g_gray_position[1] = 8;   break; // 011000000000b
        case 0x0400: g_gray_position[1] = 9;   break; // 010000000000b
        case 0x0E00: g_gray_position[1] = 9;   break; // 111000000000b
        case 0x0C00: g_gray_position[1] = 10;  break; // 110000000000b
        case 0x0800: g_gray_position[1] = 11;  break; // 100000000000b
        case 0x0000:
        default:     // 其他情况
        {
            g_gray_position[1] = 0;
        }
    }
}

/**
 * @brief 获取10mm线宽位置
 * @retval 位置值
 */
float gray_sensor_get_position_10mm(void)
{
    return g_gray_position[0];
}

/**
 * @brief 获取20mm线宽位置
 * @retval 位置值
 */
float gray_sensor_get_position_20mm(void)
{
    return g_gray_position[1];
}

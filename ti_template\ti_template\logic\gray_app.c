#include "gray_app.h"
#include "uart_driver.h"
#include <stdint.h>

/* 全局变量 */
static GrayStatus_t g_gray_status = GRAY_STATUS_NORMAL;
static uint32_t g_lost_line_count = 0;
float g_line_position_error = 0.0f;

/**
 * @brief 灰度传感器初始化
 * @retval 无
 */
void user_gray_init(void)
{
    // 初始化12路灰度传感器
    if (gray_sensor_init() != 0)
    {
        g_gray_status = GRAY_STATUS_ERROR;
        my_printf(UART_0_INST, "Gray sensor init failed!\r\n");
        return;
    }

    g_gray_status = GRAY_STATUS_NORMAL;
    g_lost_line_count = 0;
    g_line_position_error = 0.0f;

    my_printf(UART_0_INST, "12-channel gray sensor initialized successfully!\r\n");
}

/**
 * @brief 灰度传感器任务
 * @retval 无
 */
void user_gray_task(void)
{
    // 读取12路灰度传感器原始数据
    uint16_t raw_data = gray_sensor_get_raw_data();

    // 处理10mm和20mm线宽数据
    gray_sensor_process_10mm_line();
    gray_sensor_process_20mm_line();

    // 更新线位置误差
    g_line_position_error = gray_sensor_get_position_10mm();

    // 检测丢线状态
    if (raw_data == 0x0000 || raw_data == 0x0FFF)
    {
        g_lost_line_count++;
        if (g_lost_line_count > 10)
        {
            g_gray_status = GRAY_STATUS_LOST;
        }
    }
    else
    {
        g_lost_line_count = 0;
        g_gray_status = GRAY_STATUS_NORMAL;
    }

    // 调试输出（可选）
    static uint32_t debug_count = 0;
    if (++debug_count >= 100) // 每100次输出一次，降低串口负载
    {
        debug_count = 0;
        my_printf(UART_0_INST, "Gray: 0x%04X, Pos10: %.1f, Pos20: %.1f, Status: %d\r\n",
                  raw_data,
                  gray_sensor_get_position_10mm(),
                  gray_sensor_get_position_20mm(),
                  g_gray_status);
    }
}

/**
 * @brief 计算加权线位置
 * @param type: 线宽类型
 * @retval 线位置值
 */
float calculate_line_position_weighted(LineWidthType_t type)
{
    if (type == LINE_WIDTH_10MM)
    {
        return gray_sensor_get_position_10mm();
    }
    else
    {
        return gray_sensor_get_position_20mm();
    }
}

/**
 * @brief 获取灰度传感器状态
 * @retval 传感器状态
 */
GrayStatus_t get_gray_sensor_status(void)
{
    return g_gray_status;
}

/**
 * @brief 获取原始灰度数据
 * @retval 12位原始数据
 */
uint16_t get_gray_raw_data(void)
{
    return g_gray_sensor_state.state;
}



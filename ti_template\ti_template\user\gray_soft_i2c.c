#include "gray_soft_i2c.h"

/* I2C引脚操作宏定义 */
#define I2C_READ_SDA()        ((GRAY_I2C_PORT->DIN31_0 & GRAY_I2C_SDA_PIN) > 0 ? 1 : 0)
#define I2C_SDA_HIGH()        DL_GPIO_setPins(GRAY_I2C_PORT, GRAY_I2C_SDA_PIN)
#define I2C_SDA_LOW()         DL_GPIO_clearPins(GRAY_I2C_PORT, GRAY_I2C_SDA_PIN)
#define I2C_SCL_HIGH()        DL_GPIO_setPins(GRAY_I2C_PORT, GRAY_I2C_SCL_PIN)
#define I2C_SCL_LOW()         DL_GPIO_clearPins(GRAY_I2C_PORT, GRAY_I2C_SCL_PIN)

/* 私有函数声明 */
static void i2c_gpio_config(void);
static void i2c_sda_out(void);
static void i2c_sda_in(void);
static void i2c_delay(void);

/**
 * @brief I2C GPIO配置
 */
static void i2c_gpio_config(void)
{
    I2C_SDA_HIGH();
    I2C_SCL_HIGH();
    gray_i2c_stop();
}

/**
 * @brief 配置SDA为输出模式
 */
static void i2c_sda_out(void)
{
    DL_GPIO_initDigitalOutput(GRAY_I2C_SDA_IOMUX);
    DL_GPIO_enableOutput(GRAY_I2C_PORT, GRAY_I2C_SDA_PIN);
}

/**
 * @brief 配置SDA为输入模式
 */
static void i2c_sda_in(void)
{
    DL_GPIO_initDigitalInputFeatures(GRAY_I2C_SDA_IOMUX, 
                                     DL_GPIO_INVERSION_DISABLE, 
                                     DL_GPIO_RESISTOR_PULL_UP,
                                     DL_GPIO_HYSTERESIS_DISABLE, 
                                     DL_GPIO_WAKEUP_DISABLE);
}

/**
 * @brief I2C时序延时
 */
static void i2c_delay(void)
{
    volatile uint8_t i;
    for (i = 0; i < 10; i++); // 调整延时以适应I2C时序
}

/**
 * @brief I2C开始信号
 */
void gray_i2c_start(void)
{
    i2c_sda_out();
    I2C_SDA_HIGH();
    i2c_delay();
    I2C_SCL_HIGH();
    i2c_delay();
    I2C_SDA_LOW();
    i2c_delay();
    I2C_SCL_LOW();
    i2c_delay();
}

/**
 * @brief I2C停止信号
 */
void gray_i2c_stop(void)
{
    i2c_sda_out();
    I2C_SCL_LOW();
    I2C_SDA_LOW();
    I2C_SCL_HIGH();
    i2c_delay();
    I2C_SDA_HIGH();
    i2c_delay();
}

/**
 * @brief 等待应答信号
 * @retval 0: 收到应答, 1: 未收到应答
 */
uint8_t gray_i2c_wait_ack(void)
{
    uint32_t timeout = 0;
    I2C_SCL_LOW();
    I2C_SDA_HIGH();
    i2c_sda_in();
    i2c_delay();
    I2C_SCL_HIGH();
    i2c_delay();
    
    while(I2C_READ_SDA())
    {
        timeout++;
        if(timeout > 100)
        {
            gray_i2c_stop();
            return 1;
        }
    }
    I2C_SCL_LOW();
    i2c_delay();
    return 0;
}

/**
 * @brief 发送应答信号
 */
void gray_i2c_ack(void)
{
    i2c_sda_out();
    I2C_SCL_LOW();
    I2C_SDA_HIGH();
    i2c_delay();
    I2C_SDA_LOW();
    i2c_delay();
    I2C_SCL_HIGH();
    i2c_delay();
    I2C_SCL_LOW();
    i2c_delay();
    I2C_SDA_HIGH();
}

/**
 * @brief 发送非应答信号
 */
void gray_i2c_nack(void)
{
    I2C_SCL_LOW();
    i2c_sda_out();
    I2C_SDA_HIGH();
    i2c_delay();
    I2C_SCL_HIGH();
    i2c_delay();
    I2C_SCL_LOW();
    i2c_delay();
}

/**
 * @brief 发送一个字节
 * @param data: 要发送的数据
 */
void gray_i2c_send_byte(uint8_t data)
{
    uint8_t i;
    i2c_sda_out();
    I2C_SCL_LOW();
    
    for(i = 0; i < 8; i++)
    {
        if((data & 0x80) >> 7)
            I2C_SDA_HIGH();
        else
            I2C_SDA_LOW();
        data <<= 1;
        i2c_delay();
        I2C_SCL_HIGH();
        i2c_delay();
        I2C_SCL_LOW();
    }
    gray_i2c_wait_ack();
}

/**
 * @brief 发送字节不等待应答
 * @param data: 要发送的数据
 */
void gray_i2c_send_byte_no_ack(uint8_t data)
{
    uint8_t i;
    i2c_sda_out();
    I2C_SCL_LOW();
    
    for(i = 0; i < 8; i++)
    {
        if((data & 0x80) >> 7)
            I2C_SDA_HIGH();
        else
            I2C_SDA_LOW();
        data <<= 1;
        i2c_delay();
        I2C_SCL_HIGH();
        i2c_delay();
        I2C_SCL_LOW();
    }
}

/**
 * @brief 读取一个字节
 * @retval 读取到的数据
 */
uint8_t gray_i2c_read_byte(void)
{
    I2C_SDA_HIGH();
    i2c_sda_in();
    uint8_t i, data = 0;
    
    for(i = 0; i < 8; i++)
    {
        data <<= 1;
        I2C_SCL_HIGH();
        i2c_delay();
        if(I2C_READ_SDA())
        {
            data++;
        }
        I2C_SCL_LOW();
        i2c_delay();
    }
    return data;
}

/**
 * @brief 检测I2C设备
 * @param addr: 设备地址
 * @retval 0: 设备存在, 1: 设备不存在
 */
uint8_t gray_i2c_check_device(uint8_t addr)
{
    uint8_t ack;
    i2c_gpio_config();
    gray_i2c_start();
    gray_i2c_send_byte_no_ack(addr | I2C_WR);
    ack = gray_i2c_wait_ack();
    gray_i2c_stop();
    return ack;
}

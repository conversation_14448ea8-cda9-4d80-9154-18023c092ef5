#ifndef __GRAY_SOFT_I2C_H
#define __GRAY_SOFT_I2C_H

#include "bsp_system.h"

#ifdef __cplusplus
extern "C" {
#endif

/* I2C操作定义 */
#define I2C_WR	0		// 写操作bit
#define I2C_RD	1		// 读操作bit

/* 函数声明 */
void gray_i2c_start(void);                    // I2C开始信号
void gray_i2c_stop(void);                     // I2C停止信号
void gray_i2c_ack(void);                      // I2C应答信号
void gray_i2c_nack(void);                     // I2C非应答信号
uint8_t gray_i2c_wait_ack(void);              // 等待应答信号
uint8_t gray_i2c_read_byte(void);             // 读取一个字节
void gray_i2c_send_byte(uint8_t data);        // 发送一个字节
void gray_i2c_send_byte_no_ack(uint8_t data); // 发送字节不等待应答
uint8_t gray_i2c_check_device(uint8_t addr);  // 检测设备

#ifdef __cplusplus
}
#endif

#endif /* __GRAY_SOFT_I2C_H */

#ifndef __GRAY_APP_H
#define __GRAY_APP_H

#include "bsp_system.h"
#include "gray_sensor_12ch.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 线宽类型定义 */
typedef enum {
    LINE_WIDTH_10MM = 0,    // 10mm线宽
    LINE_WIDTH_20MM = 1     // 20mm线宽
} LineWidthType_t;

/* 灰度传感器状态 */
typedef enum {
    GRAY_STATUS_NORMAL = 0,     // 正常状态
    GRAY_STATUS_LOST,           // 丢线状态
    GRAY_STATUS_ERROR           // 错误状态
} GrayStatus_t;

/* 函数声明 */
void user_gray_init(void);                                    // 灰度传感器初始化
void user_gray_task(void);                                    // 灰度传感器任务
float calculate_line_position_weighted(LineWidthType_t type); // 计算加权线位置
GrayStatus_t get_gray_sensor_status(void);                   // 获取传感器状态
uint16_t get_gray_raw_data(void);                            // 获取原始数据

#ifdef __cplusplus
}
#endif

#endif /* __GRAY_APP_H */
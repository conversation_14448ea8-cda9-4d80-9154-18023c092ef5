#ifndef __GRAY_SENSOR_CONFIG_H
#define __GRAY_SENSOR_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

/* 12路灰度传感器配置参数 */

/* I2C通信配置 */
#define GRAY_I2C_TIMEOUT_MS             100     // I2C通信超时时间(ms)
#define GRAY_I2C_RETRY_COUNT            3       // I2C通信重试次数
#define GRAY_I2C_DELAY_US               10      // I2C时序延时(us)

/* 传感器设备配置 */
#define GRAY_SENSOR_DEVICE_ADDR         0x40    // PCA9555设备地址
#define GRAY_SENSOR_CHANNEL_COUNT       12      // 灰度传感器通道数
#define GRAY_SENSOR_DATA_MASK           0x0FFF  // 12位数据掩码

/* 位置计算配置 */
#define GRAY_POSITION_MIN               -11     // 最小位置值
#define GRAY_POSITION_MAX               11      // 最大位置值
#define GRAY_POSITION_CENTER            0       // 中心位置值
#define GRAY_POSITION_HISTORY_SIZE      20      // 位置历史记录大小

/* 状态检测配置 */
#define GRAY_LOST_LINE_THRESHOLD        10      // 丢线检测阈值(连续次数)
#define GRAY_ERROR_COUNT_MAX            100     // 最大错误计数
#define GRAY_ALL_BLACK                  0x0000  // 全黑状态
#define GRAY_ALL_WHITE                  0x0FFF  // 全白状态

/* 调试输出配置 */
#define GRAY_DEBUG_ENABLE               1       // 使能调试输出
#define GRAY_DEBUG_INTERVAL             100     // 调试输出间隔(任务周期数)
#define GRAY_DEBUG_VERBOSE              0       // 详细调试信息

/* 线宽检测配置 */
#define GRAY_LINE_WIDTH_10MM_PATTERNS   24      // 10mm线宽模式数量
#define GRAY_LINE_WIDTH_20MM_PATTERNS   32      // 20mm线宽模式数量

/* 滤波配置 */
#define GRAY_FILTER_ENABLE              1       // 使能位置滤波
#define GRAY_FILTER_ALPHA               0.8f    // 滤波系数(0-1)
#define GRAY_FILTER_THRESHOLD           2.0f    // 滤波阈值

/* 性能优化配置 */
#define GRAY_FAST_MODE                  1       // 快速模式(减少I2C通信)
#define GRAY_CACHE_ENABLE               1       // 使能数据缓存
#define GRAY_INTERRUPT_MODE             0       // 中断模式(暂不支持)

/* 校准配置 */
#define GRAY_AUTO_CALIBRATION           0       // 自动校准功能
#define GRAY_CALIBRATION_SAMPLES        100     // 校准采样数量
#define GRAY_CALIBRATION_THRESHOLD      50      // 校准阈值

/* 错误处理配置 */
#define GRAY_ERROR_RECOVERY_ENABLE      1       // 使能错误恢复
#define GRAY_ERROR_RECOVERY_DELAY       1000    // 错误恢复延时(ms)
#define GRAY_WATCHDOG_ENABLE            1       // 使能看门狗检测

/* 兼容性配置 */
#define GRAY_LEGACY_API_SUPPORT         1       // 支持旧版API
#define GRAY_MULTI_SENSOR_SUPPORT       0       // 多传感器支持(扩展功能)

/* 硬件相关配置 */
#define GRAY_POWER_CONTROL_ENABLE       0       // 电源控制功能
#define GRAY_RESET_PIN_ENABLE           0       // 复位引脚功能
#define GRAY_INTERRUPT_PIN_ENABLE       0       // 中断引脚功能

/* 算法配置 */
#define GRAY_WEIGHTED_AVERAGE_ENABLE    1       // 加权平均算法
#define GRAY_EDGE_DETECTION_ENABLE      1       // 边缘检测算法
#define GRAY_NOISE_FILTER_ENABLE        1       // 噪声滤波

/* 测试和验证配置 */
#define GRAY_SELF_TEST_ENABLE           1       // 自检功能
#define GRAY_PATTERN_TEST_ENABLE        0       // 模式测试功能
#define GRAY_BENCHMARK_ENABLE           0       // 性能基准测试

/* 内存配置 */
#define GRAY_STATIC_MEMORY_ONLY         1       // 仅使用静态内存
#define GRAY_BUFFER_SIZE                64      // 缓冲区大小
#define GRAY_STACK_SIZE                 256     // 栈大小(字节)

/* 时序配置 */
#define GRAY_TASK_PERIOD_MS             1       // 任务周期(ms)
#define GRAY_SAMPLE_RATE_HZ             1000    // 采样率(Hz)
#define GRAY_UPDATE_RATE_HZ             100     // 更新率(Hz)

/* 安全配置 */
#define GRAY_BOUNDS_CHECK_ENABLE        1       // 边界检查
#define GRAY_NULL_POINTER_CHECK         1       // 空指针检查
#define GRAY_OVERFLOW_PROTECTION        1       // 溢出保护

/* 编译选项 */
#ifdef DEBUG
    #undef GRAY_DEBUG_ENABLE
    #define GRAY_DEBUG_ENABLE           1
    #undef GRAY_DEBUG_VERBOSE
    #define GRAY_DEBUG_VERBOSE          1
#endif

#ifdef RELEASE
    #undef GRAY_DEBUG_ENABLE
    #define GRAY_DEBUG_ENABLE           0
    #undef GRAY_FAST_MODE
    #define GRAY_FAST_MODE              1
#endif

/* 参数验证宏 */
#define GRAY_VALIDATE_POSITION(pos)     ((pos) >= GRAY_POSITION_MIN && (pos) <= GRAY_POSITION_MAX)
#define GRAY_VALIDATE_CHANNEL(ch)       ((ch) < GRAY_SENSOR_CHANNEL_COUNT)
#define GRAY_VALIDATE_DATA(data)        (((data) & GRAY_SENSOR_DATA_MASK) == (data))

/* 位操作宏 */
#define GRAY_GET_BIT(data, bit)         (((data) >> (bit)) & 1)
#define GRAY_SET_BIT(data, bit)         ((data) |= (1 << (bit)))
#define GRAY_CLEAR_BIT(data, bit)       ((data) &= ~(1 << (bit)))
#define GRAY_TOGGLE_BIT(data, bit)      ((data) ^= (1 << (bit)))

/* 数学宏 */
#define GRAY_ABS(x)                     (((x) < 0) ? -(x) : (x))
#define GRAY_MIN(a, b)                  (((a) < (b)) ? (a) : (b))
#define GRAY_MAX(a, b)                  (((a) > (b)) ? (a) : (b))
#define GRAY_CLAMP(x, min, max)         (GRAY_MIN(GRAY_MAX((x), (min)), (max)))

/* 状态宏 */
#define GRAY_IS_NORMAL(status)          ((status) == GRAY_STATUS_NORMAL)
#define GRAY_IS_LOST(status)            ((status) == GRAY_STATUS_LOST)
#define GRAY_IS_ERROR(status)           ((status) == GRAY_STATUS_ERROR)

#ifdef __cplusplus
}
#endif

#endif /* __GRAY_SENSOR_CONFIG_H */
